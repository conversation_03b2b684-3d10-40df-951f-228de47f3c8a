#!/usr/bin/env python3
"""
Run Comprehensive CLEAR-E Experiments for Paper Results
"""

import os
import subprocess
import json
import time
from datetime import datetime

def run_experiment(dataset, model, pred_len, method, epochs=5, itr=1):
    """Run a single experiment and return results"""

    print(f"Running: {dataset} - {model} - {pred_len}h - {method}")
    start_time = time.time()

    # Set model-specific parameters
    if model == "PatchTST":
        d_model, n_heads, e_layers, d_ff = 16, 4, 3, 128
    elif model == "iTransformer":
        d_model, n_heads, e_layers, d_ff = 256, 8, 2, 256
    else:  # Default for TCN_RevIN and others
        d_model, n_heads, e_layers, d_ff = 512, 8, 2, 2048

    # Base command
    cmd = [
        "python", "-u", "run.py",
        "--dataset", dataset,
        "--model", model,
        "--seq_len", "96",
        "--pred_len", str(pred_len),
        "--batch_size", "16",
        "--learning_rate", "0.001",
        "--train_epochs", str(epochs),
        "--itr", str(itr),
        "--features", "M",
        "--d_model", str(d_model),
        "--n_heads", str(n_heads),
        "--e_layers", str(e_layers),
        "--d_ff", str(d_ff)
    ]
    
    # Add method-specific parameters
    if method == "ClearE":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "ClearE",
            "--concept_dim", "64",
            "--bottleneck_dim", "32",
            "--metadata_dim", "10",
            "--metadata_hidden_dim", "32",
            "--drift_memory_size", "10",
            "--drift_reg_weight", "0.1",
            "--use_energy_loss",
            "--high_load_threshold", "0.8",
            "--underestimate_penalty", "2.0",
            "--border_type", "online",
            "--pretrain",
            "--save_opt",
            "--only_test",
            "--val_online_lr",
            "--diff_online_lr",
            "--tune_mode", "down_up"
        ])

        # Apply the same checkpoint path fix for ClearE
        import os
        if model == "PatchTST":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
        elif model == "iTransformer":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
        else:
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"

        os.makedirs(target_path, exist_ok=True)
        if os.path.exists(f"{source_path}/checkpoint.pth") and not os.path.exists(f"{target_path}/checkpoint.pth"):
            try:
                os.symlink(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
            except OSError:
                import shutil
                shutil.copy2(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
    elif method == "Online":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "Online",
            "--border_type", "online",
            "--pretrain",
            "--save_opt",
            "--only_test"
        ])

        # Fix the checkpoint path issue by creating symlinks from the 0.001 checkpoint to the 0.0001 checkpoint
        # This is a workaround for the mismatch between the learning rate in the checkpoint path
        # and the actual learning rate used for training
        import os
        if model == "PatchTST":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
        elif model == "iTransformer":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
        else:
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"

        # Create the target directory if it doesn't exist
        os.makedirs(target_path, exist_ok=True)

        # Create a symlink to the checkpoint file
        if os.path.exists(f"{source_path}/checkpoint.pth") and not os.path.exists(f"{target_path}/checkpoint.pth"):
            print(f"Creating symlink from {source_path}/checkpoint.pth to {target_path}/checkpoint.pth")
            try:
                os.symlink(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
            except OSError:
                # If symlink fails, try copying the file
                import shutil
                shutil.copy2(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
    elif method == "Proceed":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "Proceed",
            "--concept_dim", "64",
            "--bottleneck_dim", "32",
            "--border_type", "online",
            "--pretrain",
            "--save_opt",
            "--only_test",
            "--val_online_lr",
            "--diff_online_lr",
            "--tune_mode", "down_up"
        ])

        # Apply the same checkpoint path fix for Proceed
        import os
        if model == "PatchTST":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
        elif model == "iTransformer":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
        else:
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"

        os.makedirs(target_path, exist_ok=True)
        if os.path.exists(f"{source_path}/checkpoint.pth") and not os.path.exists(f"{target_path}/checkpoint.pth"):
            try:
                os.symlink(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
            except OSError:
                import shutil
                shutil.copy2(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
    elif method == "FSNet":
        # For FSNet, we need to handle the model differently
        if model == "TCN_RevIN":
            # Use FSNet directly for TCN_RevIN
            cmd[4] = "FSNet"  # Replace model parameter
            cmd.extend([
                "--online_learning_rate", "0.00003",
                "--normalization", "RevIN",
                "--border_type", "online",
                "--save_opt",
                "--only_test"
            ])
        else:
            # For other models, use FSNet wrapper
            cmd.extend([
                "--online_learning_rate", "0.00003",
                "--online_method", "FSNet",
                "--border_type", "online",
                "--save_opt",
                "--only_test"
            ])
    elif method == "OneNet":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "OneNet",
            "--border_type", "online",
            "--save_opt",
            "--only_test"
        ])

        # Apply the same checkpoint path fix for OneNet
        import os
        if model == "PatchTST":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
        elif model == "iTransformer":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
        else:
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"

        os.makedirs(target_path, exist_ok=True)
        if os.path.exists(f"{source_path}/checkpoint.pth") and not os.path.exists(f"{target_path}/checkpoint.pth"):
            try:
                os.symlink(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
            except OSError:
                import shutil
                shutil.copy2(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
    elif method == "SOLID++":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "SOLID",
            "--border_type", "online",
            "--save_opt",
            "--only_test"
        ])

        # Apply the same checkpoint path fix for SOLID++
        import os
        if model == "PatchTST":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
        elif model == "iTransformer":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
        else:
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"

        os.makedirs(target_path, exist_ok=True)
        if os.path.exists(f"{source_path}/checkpoint.pth") and not os.path.exists(f"{target_path}/checkpoint.pth"):
            try:
                os.symlink(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
            except OSError:
                import shutil
                shutil.copy2(f"{source_path}/checkpoint.pth", f"{target_path}/checkpoint.pth")
    else:
        # Offline baseline - just train and test
        pass
    
    # Run the experiment
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
        
        if result.returncode == 0:
            # Extract results from output
            output = result.stdout
            lines = output.split('\n')
            
            for line in lines:
                if line.startswith('mse:') and 'mae:' in line:
                    parts = line.split(',')
                    mse = float(parts[0].split(':')[1].strip())
                    mae = float(parts[1].split(':')[1].strip())
                    
                    duration = time.time() - start_time
                    return {
                        'dataset': dataset,
                        'model': model,
                        'pred_len': pred_len,
                        'method': method,
                        'mse': mse,
                        'mae': mae,
                        'duration': duration,
                        'success': True,
                        'output': output
                    }
            
            # If no results found in output
            return {
                'dataset': dataset,
                'model': model,
                'pred_len': pred_len,
                'method': method,
                'success': False,
                'error': 'No results found in output',
                'output': output,
                'duration': time.time() - start_time
            }
        else:
            return {
                'dataset': dataset,
                'model': model,
                'pred_len': pred_len,
                'method': method,
                'success': False,
                'error': result.stderr,
                'output': result.stdout,
                'duration': time.time() - start_time
            }
            
    except subprocess.TimeoutExpired:
        return {
            'dataset': dataset,
            'model': model,
            'pred_len': pred_len,
            'method': method,
            'success': False,
            'error': 'Timeout',
            'duration': time.time() - start_time
        }
    except Exception as e:
        return {
            'dataset': dataset,
            'model': model,
            'pred_len': pred_len,
            'method': method,
            'success': False,
            'error': str(e),
            'duration': time.time() - start_time
        }

def ensure_model_trained(dataset, model, epochs=5):
    """Ensure the base model is trained before running online experiments"""

    # Check for existing checkpoints with different learning rates and model parameters
    # This handles the case where models like PatchTST and iTransformer have different parameters
    possible_checkpoints = [
        f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0/checkpoint.pth",
        f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0/checkpoint.pth",  # PatchTST
        f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0/checkpoint.pth",  # iTransformer
        f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.001_dm128_nh8_el2_dl1_df128_fc3_ebtimeF_dtTrue_test_0/checkpoint.pth",  # iTransformer for some datasets
    ]

    for checkpoint_path in possible_checkpoints:
        if os.path.exists(checkpoint_path):
            print(f"✓ Model {model} already trained for {dataset}")
            print(f"  Found checkpoint: {checkpoint_path}")
            return True

    print(f"Training {model} for {dataset}...")

    # Set model-specific parameters
    if model == "PatchTST":
        d_model, n_heads, e_layers, d_ff = 16, 4, 3, 128
    elif model == "iTransformer":
        d_model, n_heads, e_layers, d_ff = 256, 8, 2, 256
    else:  # Default for TCN_RevIN and others
        d_model, n_heads, e_layers, d_ff = 512, 8, 2, 2048

    cmd = [
        "python", "-u", "run.py",
        "--dataset", dataset,
        "--model", model,
        "--seq_len", "96",
        "--pred_len", "24",
        "--batch_size", "16",
        "--learning_rate", "0.001",
        "--train_epochs", str(epochs),
        "--itr", "1",
        "--features", "M",
        "--d_model", str(d_model),
        "--n_heads", str(n_heads),
        "--e_layers", str(e_layers),
        "--d_ff", str(d_ff)
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
        if result.returncode == 0:
            print(f"✓ Successfully trained {model} for {dataset}")
            return True
        else:
            print(f"✗ Failed to train {model} for {dataset}: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Error training {model} for {dataset}: {e}")
        return False

def main():
    # Comprehensive experimental configuration - ALL available models and methods
    datasets = ["ETTh1", "ETTh2", "ETTm1", "ETTm2", "ECL", "Weather", "Traffic"]
    models = ["PatchTST", "iTransformer"]  # Skip TCN_RevIN due to dimension issues, focus on working models
    pred_lens = [24, 48, 96]
    methods = ["Offline", "Online", "FSNet", "OneNet", "SOLID++", "Proceed", "ClearE"]  # All available methods

    # Setup logging
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = "logs/comprehensive_experiments"
    os.makedirs(log_dir, exist_ok=True)
    log_file = f"{log_dir}/experiment_log_{timestamp}.txt"

    # Create a function to log messages to both console and file
    def log_message(message):
        print(message)
        with open(log_file, "a") as f:
            f.write(message + "\n")

    log_message("=== Comprehensive CLEAR-E Experiments ===")
    log_message(f"Datasets: {datasets}")
    log_message(f"Models: {models}")
    log_message(f"Prediction horizons: {pred_lens}")
    log_message(f"Methods: {methods}")
    log_message("==========================================")

    # Create results directory
    os.makedirs("results/comprehensive_experiments", exist_ok=True)

    all_results = []
    total_experiments = len(datasets) * len(models) * len(pred_lens) * len(methods)
    current_experiment = 0

    # Track successful and failed experiments
    success_count = 0
    failure_count = 0
    skipped_count = 0

    # Create a progress tracking file that can be monitored
    progress_file = f"{log_dir}/progress_{timestamp}.txt"

    for dataset in datasets:
        for model in models:
            # Ensure base model is trained
            if not ensure_model_trained(dataset, model):
                log_message(f"Skipping {dataset}-{model} due to training failure")
                skipped_count += len(pred_lens) * len(methods)
                continue

            for pred_len in pred_lens:
                for method in methods:
                    current_experiment += 1
                    log_message(f"\n=== Experiment {current_experiment}/{total_experiments} ===")

                    # Update progress file
                    with open(progress_file, "w") as f:
                        f.write(f"Progress: {current_experiment}/{total_experiments}\n")
                        f.write(f"Success: {success_count}, Failed: {failure_count}, Skipped: {skipped_count}\n")
                        f.write(f"Current: {dataset} - {model} - {pred_len}h - {method}\n")

                    try:
                        result = run_experiment(dataset, model, pred_len, method)
                        all_results.append(result)

                        if result['success']:
                            log_message(f"✓ Success: MSE={result['mse']:.4f}, MAE={result['mae']:.4f}")
                            success_count += 1
                        else:
                            log_message(f"✗ Failed: {result.get('error', 'Unknown error')}")
                            failure_count += 1

                        log_message(f"Duration: {result['duration']:.1f}s")
                    except Exception as e:
                        log_message(f"✗ Exception during experiment: {str(e)}")
                        failure_count += 1
                        all_results.append({
                            'dataset': dataset,
                            'model': model,
                            'pred_len': pred_len,
                            'method': method,
                            'success': False,
                            'error': str(e),
                            'duration': 0
                        })

    # Save results
    results_file = f"results/comprehensive_experiments/results_{timestamp}.json"

    summary = {
        'timestamp': timestamp,
        'total_experiments': total_experiments,
        'successful_experiments': success_count,
        'failed_experiments': failure_count,
        'skipped_experiments': skipped_count,
        'results': all_results
    }

    with open(results_file, 'w') as f:
        json.dump(summary, f, indent=2)

    # Final summary
    log_message(f"\n=== Final Experiment Summary ===")
    log_message(f"Total experiments planned: {total_experiments}")
    log_message(f"Successful: {success_count}")
    log_message(f"Failed: {failure_count}")
    log_message(f"Skipped: {skipped_count}")
    log_message(f"Success rate: {success_count/(total_experiments-skipped_count)*100:.1f}% (of attempted)")
    log_message(f"Results saved to: {results_file}")
    log_message(f"Log saved to: {log_file}")

    # Create summary table
    create_summary_table(all_results, timestamp)

    # Update final progress
    with open(progress_file, "w") as f:
        f.write("COMPLETED\n")
        f.write(f"Final Results: Success: {success_count}, Failed: {failure_count}, Skipped: {skipped_count}\n")
        f.write(f"Success rate: {success_count/(total_experiments-skipped_count)*100:.1f}%\n")

def create_summary_table(results, timestamp):
    """Create a summary table of results"""
    
    # Group results by dataset and method
    summary_data = {}
    
    for result in results:
        if not result['success']:
            continue
            
        key = f"{result['dataset']}_{result['method']}"
        if key not in summary_data:
            summary_data[key] = {
                'dataset': result['dataset'],
                'method': result['method'],
                'mse_values': [],
                'mae_values': []
            }
        
        summary_data[key]['mse_values'].append(result['mse'])
        summary_data[key]['mae_values'].append(result['mae'])
    
    # Calculate statistics
    import numpy as np
    
    table_data = []
    for key, data in summary_data.items():
        if data['mse_values']:
            mse_mean = np.mean(data['mse_values'])
            mse_std = np.std(data['mse_values'])
            mae_mean = np.mean(data['mae_values'])
            mae_std = np.std(data['mae_values'])
            
            table_data.append({
                'dataset': data['dataset'],
                'method': data['method'],
                'mse_mean': mse_mean,
                'mse_std': mse_std,
                'mae_mean': mae_mean,
                'mae_std': mae_std,
                'n_results': len(data['mse_values'])
            })
    
    # Save table
    table_file = f"results/comprehensive_experiments/summary_table_{timestamp}.json"
    with open(table_file, 'w') as f:
        json.dump(table_data, f, indent=2)
    
    print(f"Summary table saved to: {table_file}")
    
    # Print table
    print("\n=== Results Summary Table ===")
    print(f"{'Dataset':<15} {'Method':<10} {'MSE':<15} {'MAE':<15} {'N':<5}")
    print("-" * 65)
    
    for row in sorted(table_data, key=lambda x: (x['dataset'], x['method'])):
        mse_str = f"{row['mse_mean']:.4f}±{row['mse_std']:.4f}"
        mae_str = f"{row['mae_mean']:.4f}±{row['mae_std']:.4f}"
        print(f"{row['dataset']:<15} {row['method']:<10} {mse_str:<15} {mae_str:<15} {row['n_results']:<5}")

if __name__ == "__main__":
    main()
