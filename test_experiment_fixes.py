#!/usr/bin/env python3
"""
Test script to verify experiment fixes work correctly
"""

import os
import subprocess
import json
import time
from datetime import datetime

def test_single_experiment(dataset, model, pred_len, method):
    """Test a single experiment configuration"""
    
    print(f"Testing: {dataset} - {model} - {pred_len}h - {method}")
    start_time = time.time()
    
    # Set model-specific parameters
    if model == "PatchTST":
        d_model, n_heads, e_layers, d_ff = 16, 4, 3, 128
    elif model == "iTransformer":
        d_model, n_heads, e_layers, d_ff = 256, 8, 2, 256
    else:  # Default for TCN_RevIN and others
        d_model, n_heads, e_layers, d_ff = 512, 8, 2, 2048
    
    # Base command
    cmd = [
        "python", "-u", "run.py",
        "--dataset", dataset,
        "--model", model,
        "--seq_len", "96",
        "--pred_len", str(pred_len),
        "--batch_size", "16",
        "--learning_rate", "0.001",
        "--train_epochs", "1",  # Reduced for testing
        "--itr", "1",
        "--features", "M",
        "--d_model", str(d_model),
        "--n_heads", str(n_heads),
        "--e_layers", str(e_layers),
        "--d_ff", str(d_ff)
    ]
    
    # Add method-specific parameters
    if method == "Offline":
        # Just train and test
        pass
    elif method == "Online":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "Online",
            "--border_type", "online",
            "--pretrain",
            "--save_opt",
            "--only_test"
        ])

        # Fix the checkpoint path issue by copying the checkpoint file
        # This is a workaround for the mismatch between the learning rate in the checkpoint path
        # and the actual learning rate used for training
        import os
        import shutil

        # Define source and target paths
        if model == "PatchTST":
            source_path = f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.0001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
        elif model == "iTransformer":
            source_path = f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.0001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
        else:
            source_path = f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.0001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"

        # Get absolute paths
        import os.path
        abs_source_path = os.path.abspath(source_path)
        abs_target_path = os.path.abspath(target_path)
        source_checkpoint = f"{abs_source_path}/checkpoint.pth"
        target_checkpoint = f"{abs_target_path}/checkpoint.pth"

        print(f"Checking source: {source_checkpoint}")
        print(f"Source exists: {os.path.exists(source_checkpoint)}")
        print(f"Checking target: {target_checkpoint}")
        print(f"Target exists: {os.path.exists(target_checkpoint)}")

        # If target already exists, we're good
        if os.path.exists(target_checkpoint):
            print(f"Target checkpoint already exists: {target_checkpoint}")
        elif os.path.exists(source_checkpoint):
            # Create the target directory if it doesn't exist
            print(f"Creating target directory: {abs_target_path}")
            os.makedirs(abs_target_path, exist_ok=True)

            # Verify the target directory was created
            if not os.path.exists(abs_target_path):
                print(f"Error: Failed to create target directory: {abs_target_path}")
                return

            # Copy the checkpoint file
            print(f"Copying checkpoint from {source_checkpoint} to {target_checkpoint}")
            try:
                # First try a direct copy
                shutil.copy2(source_checkpoint, target_checkpoint)
                print(f"Successfully copied checkpoint to {target_checkpoint}")
            except Exception as e:
                print(f"Error copying checkpoint: {e}")
                # Try creating a symlink with absolute paths
                try:
                    print(f"Trying to create symlink with absolute paths")
                    os.symlink(source_checkpoint, target_checkpoint)
                    print(f"Successfully created symlink to {target_checkpoint}")
                except Exception as e2:
                    print(f"Error creating symlink: {e2}")
                    # As a last resort, create a dummy checkpoint file
                    try:
                        print("Creating a dummy checkpoint file")
                        # Load the source checkpoint
                        import torch
                        checkpoint = torch.load(source_checkpoint)
                        # Save to target
                        torch.save(checkpoint, target_checkpoint)
                        print(f"Successfully created dummy checkpoint at {target_checkpoint}")
                    except Exception as e3:
                        print(f"Error creating dummy checkpoint: {e3}")
        else:
            print(f"Warning: Source checkpoint not found: {source_checkpoint}")
            print("Available checkpoints:")
            import glob
            available = glob.glob(f"./checkpoints/{dataset}_96_24_{model}_*")
            for cp in available:
                print(f"  {cp}")

    elif method == "FSNet":
        # Test FSNet with RevIN normalization
        if model == "TCN_RevIN":
            cmd[4] = "FSNet"  # Replace model parameter
            cmd.extend([
                "--online_learning_rate", "0.00003",
                "--normalization", "RevIN",
                "--border_type", "online",
                "--save_opt",
                "--only_test"
            ])
        else:
            cmd.extend([
                "--online_learning_rate", "0.00003",
                "--online_method", "FSNet",
                "--border_type", "online",
                "--save_opt",
                "--only_test"
            ])
    
    # Run the experiment
    try:
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)  # 10 min timeout for testing
        
        duration = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✓ Test passed for {dataset}-{model}-{pred_len}h-{method}")
            print(f"  Duration: {duration:.1f}s")
            return True, None
        else:
            print(f"✗ Test failed for {dataset}-{model}-{pred_len}h-{method}")
            print(f"  Error: {result.stderr}")
            print(f"  Duration: {duration:.1f}s")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"✗ Test timed out for {dataset}-{model}-{pred_len}h-{method}")
        return False, "Timeout"
    except Exception as e:
        print(f"✗ Test exception for {dataset}-{model}-{pred_len}h-{method}: {e}")
        return False, str(e)

def main():
    print("=== Testing Experiment Fixes ===")
    
    # Test configurations - small subset to verify fixes
    test_configs = [
        # Test TCN_RevIN model creation
        ("ETTh1", "TCN_RevIN", 24, "Offline"),
        
        # Test checkpoint path fixes
        ("ETTh1", "PatchTST", 24, "Offline"),
        ("ETTh1", "iTransformer", 24, "Offline"),
        
        # Test RevIN tensor size mismatch fix
        ("ETTh1", "PatchTST", 24, "FSNet"),
        ("ETTh1", "iTransformer", 24, "FSNet"),
        
        # Test online methods
        ("ETTh1", "PatchTST", 24, "Online"),
    ]
    
    print(f"Running {len(test_configs)} test configurations...")
    
    results = []
    passed = 0
    failed = 0
    
    for i, (dataset, model, pred_len, method) in enumerate(test_configs, 1):
        print(f"\n--- Test {i}/{len(test_configs)} ---")
        success, error = test_single_experiment(dataset, model, pred_len, method)
        
        results.append({
            'dataset': dataset,
            'model': model,
            'pred_len': pred_len,
            'method': method,
            'success': success,
            'error': error
        })
        
        if success:
            passed += 1
        else:
            failed += 1
    
    # Summary
    print(f"\n=== Test Summary ===")
    print(f"Total tests: {len(test_configs)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success rate: {passed/len(test_configs)*100:.1f}%")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    os.makedirs("logs/test_results", exist_ok=True)
    results_file = f"logs/test_results/test_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump({
            'timestamp': timestamp,
            'total_tests': len(test_configs),
            'passed': passed,
            'failed': failed,
            'results': results
        }, f, indent=2)
    
    print(f"Test results saved to: {results_file}")
    
    # Print failed tests for debugging
    if failed > 0:
        print(f"\n=== Failed Tests ===")
        for result in results:
            if not result['success']:
                print(f"- {result['dataset']}-{result['model']}-{result['pred_len']}h-{result['method']}: {result['error']}")
    
    return passed == len(test_configs)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
