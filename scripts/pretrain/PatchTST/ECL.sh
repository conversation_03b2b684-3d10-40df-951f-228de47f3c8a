if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/online" ]; then
    mkdir ./logs/online
fi

seq_len=512
data=ECL
model_name=PatchTST
train_epochs=100
pct=0.2

for pred_len in 24 48 96
do
for learning_rate in 0.0001
do
  filename=logs/online/$model_name'_'$data'_'$pred_len'_lr'$learning_rate.log
  python -u -m torch.distributed.launch --nproc_per_node=2 run.py \
    --dataset $data --border_type 'online' \
    --model $model_name \
    --seq_len $seq_len \
    --pred_len $pred_len \
    --itr 3 --skip $filename \
    --pin_gpu True --reduce_bs False \
    --save_opt \
    --batch_size 32 \
    --train_epochs $train_epochs \
    --pct $pct \
    --patience 10 \
    --learning_rate $learning_rate >> $filename 2>&1
done
done
#for pred_len in 24 48 96
#do
#for learning_rate in 0.0001
#do
#  python -u run.py \
#    --dataset $data --border_type 'online' \
#    --model $model_name \
#    --seq_len $seq_len \
#    --pred_len $pred_len \
#    --itr 3 --only_test \
#    --pin_gpu True --reduce_bs False \
#    --save_opt \
#    --batch_size 16 \
#    --train_epochs $train_epochs \
#    --pct $pct \
#    --patience 10 \
#    --learning_rate $learning_rate > logs/online/$model_name'_'$data'_'$pred_len'_lr'$learning_rate.log 2>&1
#done
#done