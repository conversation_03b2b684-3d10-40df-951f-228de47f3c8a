import torch
import torch.nn as nn
from models.TCN import Model as TCNModel, Model_Ensemble as TCNModel_Ensemble
from models.normalization import ForecastModel


class Model(nn.Module):
    """TCN model with RevIN normalization"""
    def __init__(self, args):
        super().__init__()
        # Create base TCN model
        tcn_model = TCNModel(args)
        
        # Wrap with RevIN normalization
        self.model = ForecastModel(
            backbone=tcn_model,
            num_features=args.enc_in,
            seq_len=args.seq_len,
            process_method='RevIN'
        )
    
    def forward(self, x, x_mark=None):
        return self.model(x, x_mark)


class Model_Ensemble(nn.Module):
    """TCN ensemble model with RevIN normalization"""
    def __init__(self, args):
        super().__init__()
        # Create base TCN ensemble model
        tcn_model = TCNModel_Ensemble(args)
        
        # Wrap with RevIN normalization
        self.model = ForecastModel(
            backbone=tcn_model,
            num_features=args.enc_in,
            seq_len=args.seq_len,
            process_method='RevIN'
        )
    
    def forward(self, x, x_mark=None, w1=0.5, w2=0.5):
        return self.model(x, x_mark, w1=w1, w2=w2)
    
    def forward_individual(self, x, x_mark):
        return self.model.backbone.forward_individual(x, x_mark)
