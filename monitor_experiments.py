#!/usr/bin/env python3
"""
Monitor experimental progress and collect results
"""

import os
import time
import json
import subprocess
from datetime import datetime
from pathlib import Path

def check_local_experiments():
    """Check if local experiments are still running"""
    try:
        # Check if there are any Python processes running our experiment script
        result = subprocess.run(['pgrep', '-f', 'run_comprehensive_experiments.py'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def check_cluster_job(job_id="25597192"):
    """Check cluster job status"""
    try:
        result = subprocess.run(['bjobs', job_id], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:
                status_line = lines[1]
                status = status_line.split()[2]
                return status
        return "UNKNOWN"
    except:
        return "ERROR"

def collect_results():
    """Collect available results from both local and cluster experiments"""
    results = []
    
    # Check for local results
    local_results_dir = Path("results/comprehensive_experiments")
    if local_results_dir.exists():
        for result_file in local_results_dir.glob("results_*.json"):
            try:
                with open(result_file, 'r') as f:
                    data = json.load(f)
                    for result in data.get('results', []):
                        if result.get('success', False):
                            results.append({
                                'source': 'local',
                                'dataset': result['dataset'],
                                'model': result['model'],
                                'pred_len': result['pred_len'],
                                'method': result['method'],
                                'mse': result['mse'],
                                'mae': result['mae'],
                                'duration': result['duration']
                            })
            except Exception as e:
                print(f"Error reading {result_file}: {e}")
    
    # Check for cluster results
    cluster_results_dir = Path("results/clear_e_experiments")
    if cluster_results_dir.exists():
        for result_file in cluster_results_dir.glob("experiment_summary.json"):
            try:
                with open(result_file, 'r') as f:
                    data = json.load(f)
                    for key, result in data.get('results', {}).items():
                        results.append({
                            'source': 'cluster',
                            'dataset': result['dataset'],
                            'model': result['model'],
                            'pred_len': result['pred_len'],
                            'method': result['method'],
                            'mse': result['mse'],
                            'mae': result['mae']
                        })
            except Exception as e:
                print(f"Error reading {result_file}: {e}")
    
    return results

def print_progress_summary(results):
    """Print a summary of current progress"""
    print(f"\n=== Experiment Progress Summary ({datetime.now().strftime('%H:%M:%S')}) ===")
    
    # Group by source
    local_results = [r for r in results if r['source'] == 'local']
    cluster_results = [r for r in results if r['source'] == 'cluster']
    
    print(f"Local experiments: {len(local_results)} results")
    print(f"Cluster experiments: {len(cluster_results)} results")
    print(f"Total results: {len(results)}")
    
    # Group by dataset and method
    summary = {}
    for result in results:
        key = f"{result['dataset']}_{result['method']}"
        if key not in summary:
            summary[key] = []
        summary[key].append(result)
    
    print(f"\nResults by dataset and method:")
    for key in sorted(summary.keys()):
        count = len(summary[key])
        avg_mse = sum(r['mse'] for r in summary[key]) / count
        avg_mae = sum(r['mae'] for r in summary[key]) / count
        print(f"  {key}: {count} results, MSE={avg_mse:.4f}, MAE={avg_mae:.4f}")
    
    print("=" * 60)

def main():
    print("Starting experiment monitoring...")
    print("Press Ctrl+C to stop monitoring")
    
    try:
        while True:
            # Check experiment status
            local_running = check_local_experiments()
            cluster_status = check_cluster_job()
            
            print(f"\n--- Status Check ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ---")
            print(f"Local experiments: {'RUNNING' if local_running else 'STOPPED'}")
            print(f"Cluster job: {cluster_status}")
            
            # Collect and summarize results
            results = collect_results()
            if results:
                print_progress_summary(results)
                
                # Save consolidated results
                consolidated_file = "results/consolidated_results.json"
                os.makedirs("results", exist_ok=True)
                with open(consolidated_file, 'w') as f:
                    json.dump({
                        'timestamp': datetime.now().isoformat(),
                        'local_running': local_running,
                        'cluster_status': cluster_status,
                        'total_results': len(results),
                        'results': results
                    }, f, indent=2)
                
                print(f"Consolidated results saved to: {consolidated_file}")
            else:
                print("No results available yet.")
            
            # Check if both experiments are done
            if not local_running and cluster_status in ['DONE', 'EXIT', 'UNKNOWN']:
                print("\n=== All experiments appear to be completed ===")
                break
            
            # Wait before next check
            print(f"Waiting 60 seconds before next check...")
            time.sleep(60)
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user.")
        
        # Final results summary
        results = collect_results()
        if results:
            print(f"\nFinal results summary: {len(results)} total results")
            print_progress_summary(results)

if __name__ == "__main__":
    main()
