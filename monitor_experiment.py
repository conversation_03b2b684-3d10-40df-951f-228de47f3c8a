#!/usr/bin/env python3
"""
Monitor experiment progress
"""

import os
import time
import glob
import json
from datetime import datetime

def get_latest_log_file():
    """Get the latest experiment log file"""
    log_files = glob.glob("logs/clear_e_experiments_*.out")
    if not log_files:
        return None
    return max(log_files, key=os.path.getctime)

def get_latest_progress_file():
    """Get the latest progress file"""
    progress_files = glob.glob("logs/comprehensive_experiments/progress_*.txt")
    if not progress_files:
        return None
    return max(progress_files, key=os.path.getctime)

def monitor_experiment():
    """Monitor the experiment progress"""
    print("=== CLEAR-E Experiment Monitor ===")
    print(f"Started monitoring at: {datetime.now()}")
    print("Press Ctrl+C to stop monitoring\n")
    
    last_log_size = 0
    last_progress_content = ""
    
    try:
        while True:
            # Check main log file
            log_file = get_latest_log_file()
            if log_file and os.path.exists(log_file):
                current_size = os.path.getsize(log_file)
                if current_size > last_log_size:
                    # Read new content
                    with open(log_file, 'r') as f:
                        f.seek(last_log_size)
                        new_content = f.read()
                        if new_content.strip():
                            print("=== New Log Content ===")
                            print(new_content)
                            print("=" * 50)
                    last_log_size = current_size
            
            # Check progress file
            progress_file = get_latest_progress_file()
            if progress_file and os.path.exists(progress_file):
                with open(progress_file, 'r') as f:
                    content = f.read().strip()
                    if content != last_progress_content:
                        print(f"=== Progress Update ===")
                        print(content)
                        print("=" * 30)
                        last_progress_content = content
                        
                        # Check if completed
                        if content.startswith("COMPLETED"):
                            print("Experiment completed!")
                            break
            
            # Check for results files
            results_files = glob.glob("results/comprehensive_experiments/results_*.json")
            if results_files:
                latest_results = max(results_files, key=os.path.getctime)
                try:
                    with open(latest_results, 'r') as f:
                        results = json.load(f)
                        print(f"=== Current Results Summary ===")
                        print(f"Successful: {results.get('successful_experiments', 0)}")
                        print(f"Failed: {results.get('failed_experiments', 0)}")
                        print(f"Skipped: {results.get('skipped_experiments', 0)}")
                        print("=" * 40)
                except:
                    pass
            
            time.sleep(30)  # Check every 30 seconds
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user")
    except Exception as e:
        print(f"Error during monitoring: {e}")

def show_final_summary():
    """Show final experiment summary"""
    print("\n=== Final Summary ===")
    
    # Find latest results file
    results_files = glob.glob("results/comprehensive_experiments/results_*.json")
    if results_files:
        latest_results = max(results_files, key=os.path.getctime)
        try:
            with open(latest_results, 'r') as f:
                results = json.load(f)
                
            print(f"Total experiments: {results.get('total_experiments', 0)}")
            print(f"Successful: {results.get('successful_experiments', 0)}")
            print(f"Failed: {results.get('failed_experiments', 0)}")
            print(f"Skipped: {results.get('skipped_experiments', 0)}")
            
            success_rate = results.get('successful_experiments', 0) / max(1, results.get('total_experiments', 1) - results.get('skipped_experiments', 0)) * 100
            print(f"Success rate: {success_rate:.1f}%")
            
            print(f"\nResults file: {latest_results}")
            
            # Show summary table if available
            summary_files = glob.glob("results/comprehensive_experiments/summary_table_*.json")
            if summary_files:
                latest_summary = max(summary_files, key=os.path.getctime)
                print(f"Summary table: {latest_summary}")
                
        except Exception as e:
            print(f"Error reading results: {e}")
    else:
        print("No results files found")
    
    # Show latest log file
    log_file = get_latest_log_file()
    if log_file:
        print(f"Latest log file: {log_file}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "summary":
        show_final_summary()
    else:
        monitor_experiment()
        show_final_summary()
